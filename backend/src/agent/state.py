from __future__ import annotations

from dataclasses import dataclass, field
from typing import TypedDict, Optional, List, Dict, Any

from langgraph.graph import add_messages
from typing_extensions import Annotated


import operator


# 插画生成Agent的主状态
class IllustrationState(TypedDict):
    """插画生成Agent的主状态结构"""
    messages: Annotated[list, add_messages]

    # 输入处理相关
    user_input: str  # 用户原始输入
    processed_story: str  # 处理后的故事内容
    processed_story_en: str  # 处理后的故事内容（英文版）
    story_type: str  # 故事类型：summary/expansion

    # 故事分段与分镜
    story_segments: Annotated[list, operator.add]  # 故事分段
    storyboards: Annotated[list, operator.add]  # 分镜描述

    # 角色信息
    characters: Annotated[list, operator.add]  # 角色信息列表
    main_character: Optional[Dict[str, Any]]  # 主角信息
    character_base_images: Annotated[list, operator.add]  # 角色基准图

    # 图像生成
    scene_prompts: Annotated[list, operator.add]  # 优化后的场景提示词
    generated_images: Annotated[list, operator.add]  # 生成的图片
    final_illustration: Optional[str]  # 最终合并的插画

    # 配置参数
    style_preference: str  # 风格偏好
    num_panels: int  # 分镜数量
    reasoning_model: str


# 角色信息结构
class Character(TypedDict):
    """角色信息结构"""
    name: str
    description: str
    appearance: str  # 外貌特征
    style: str  # 风格设定
    role: str  # 角色类型：main/supporting
    base_image_url: Optional[str]  # 基准图URL


# 分镜信息结构
class Storyboard(TypedDict):
    """分镜信息结构"""
    panel_id: int
    scene_description: str  # 场景描述
    characters_involved: List[str]  # 涉及的角色
    environment: str  # 环境描述
    action: str  # 动作描述
    mood: str  # 氛围
    camera_angle: str  # 镜头角度


# 图像生成请求结构
class ImageGenerationRequest(TypedDict):
    """图像生成请求结构"""
    prompt: str
    style: str
    reference_image: Optional[str]  # 用于图生图的参考图
    generation_type: str  # text2img/img2img


# 原有的搜索相关状态（保留兼容性）
class OverallState(TypedDict):
    messages: Annotated[list, add_messages]
    search_query: Annotated[list, operator.add]
    web_research_result: Annotated[list, operator.add]
    sources_gathered: Annotated[list, operator.add]
    initial_search_query_count: int
    max_research_loops: int
    research_loop_count: int
    reasoning_model: str


class ReflectionState(TypedDict):
    is_sufficient: bool
    knowledge_gap: str
    follow_up_queries: Annotated[list, operator.add]
    research_loop_count: int
    number_of_ran_queries: int


class Query(TypedDict):
    query: str
    rationale: str


class QueryGenerationState(TypedDict):
    search_query: list[Query]


class WebSearchState(TypedDict):
    search_query: str
    id: str


@dataclass(kw_only=True)
class SearchStateOutput:
    running_summary: str = field(default=None)  # Final report
