#!/usr/bin/env python3
"""
简化的进度回调测试 - 验证image_merger节点的进度回调修复
"""

import sys
import os
import uuid
import time

# 添加正确的路径
current_dir = os.path.dirname(os.path.abspath(__file__))
src_dir = os.path.join(current_dir, 'src')
sys.path.insert(0, src_dir)

def test_image_merger_progress():
    """测试image_merger节点的进度回调修复"""
    print("🧪 测试image_merger节点进度回调修复...")
    
    try:
        from agent.progress_manager import ProgressManager
        from agent.illustration_nodes import image_merger
        from agent.configuration import Configuration
        from langchain_core.runnables import RunnableConfig
        
        # 创建进度管理器
        progress_manager = ProgressManager()
        session_id = str(uuid.uuid4())
        progress_manager.start_session(session_id)
        
        print(f"📍 创建会话: {session_id}")
        
        # 创建进度回调函数
        received_updates = []
        def progress_callback(node_name: str, status: str, data=None):
            update_info = f"{node_name} -> {status}"
            print(f"📊 进度更新: {update_info}")
            received_updates.append((node_name, status, time.time()))
            progress_manager.update_progress(session_id, node_name, status, data)
        
        # 模拟image_merger节点的状态
        mock_state = {
            "progress_callback": progress_callback,
            "generated_images": [
                {
                    "panel_id": 1,
                    "success": True,
                    "image_url": "mock://image1.jpg"
                },
                {
                    "panel_id": 2,
                    "success": True,
                    "image_url": "mock://image2.jpg"
                }
            ],
            "num_panels": 2
        }
        
        # 创建配置
        config = RunnableConfig(configurable={})
        
        print("🔄 执行image_merger节点...")
        
        # 执行image_merger节点
        result = image_merger(mock_state, config)
        
        print("✅ image_merger节点执行完成")
        print(f"📋 返回结果: {result}")
        
        # 检查进度更新
        print(f"\n📈 收到的进度更新: {len(received_updates)} 个")
        
        # 检查是否收到了image_merger的进度更新
        image_merger_updates = [u for u in received_updates if u[0] == 'image_merger']
        
        if len(image_merger_updates) >= 2:  # 应该有in_progress和completed
            print("✅ image_merger进度回调修复成功！")
            for node_name, status, timestamp in image_merger_updates:
                print(f"  - {node_name}: {status}")
            return True
        else:
            print("❌ image_merger进度回调仍有问题")
            print(f"  实际收到: {image_merger_updates}")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_progress_manager_improvements():
    """测试进度管理器的改进"""
    print("\n🔧 测试进度管理器改进...")
    
    try:
        from agent.progress_manager import ProgressManager
        
        progress_manager = ProgressManager()
        session_id = str(uuid.uuid4())
        progress_manager.start_session(session_id)
        
        # 添加一些进度更新
        progress_manager.update_progress(session_id, "test_node1", "in_progress")
        progress_manager.update_progress(session_id, "test_node1", "completed")
        progress_manager.update_progress(session_id, "test_node2", "in_progress")
        
        # 测试get_all_progress_updates功能
        all_progress = progress_manager.get_all_progress_updates(session_id)
        print(f"📊 历史进度数量: {len(all_progress)}")
        
        # 测试get_progress_updates功能（会清空待推送列表）
        pending_updates = progress_manager.get_progress_updates(session_id)
        print(f"📨 待推送更新: {len(pending_updates)}")
        
        # 再次获取历史进度（不应该被清空）
        all_progress_again = progress_manager.get_all_progress_updates(session_id)
        print(f"📊 历史进度数量（再次获取）: {len(all_progress_again)}")
        
        # 验证功能
        history_preserved = len(all_progress_again) == len(all_progress) >= 3  # 应该有至少3个更新
        pending_available = len(pending_updates) >= 3  # 应该有待推送的更新
        
        print(f"📋 验证结果:")
        print(f"  - 历史进度保持: {'✅' if history_preserved else '❌'} ({len(all_progress_again)}/{len(all_progress)})")
        print(f"  - 待推送更新: {'✅' if pending_available else '❌'} ({len(pending_updates)})")
        
        if history_preserved and pending_available:
            print("✅ 进度管理器改进验证成功！")
            return True
        else:
            print("❌ 进度管理器改进有问题")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🚀 开始进度回调修复验证测试")
    print("=" * 50)
    
    # 测试image_merger进度回调修复
    merger_ok = test_image_merger_progress()
    
    # 测试进度管理器改进
    manager_ok = test_progress_manager_improvements()
    
    print("\n" + "=" * 50)
    print("📊 测试结果总结:")
    print(f"  - image_merger进度回调: {'✅ 修复成功' if merger_ok else '❌ 仍有问题'}")
    print(f"  - 进度管理器改进: {'✅ 正常工作' if manager_ok else '❌ 有问题'}")
    
    if merger_ok and manager_ok:
        print("\n🎉 进度回调修复验证通过！")
        print("\n📋 修复内容总结:")
        print("  1. ✅ 为image_merger节点添加了完整的进度回调")
        print("  2. ✅ 支持in_progress、completed、error三种状态")
        print("  3. ✅ 改进了进度管理器的历史进度获取功能")
        print("  4. ✅ 增强了SSE端点的进度推送机制")
        print("\n🔧 下一步操作:")
        print("  1. 重启后端服务器")
        print("  2. 清除浏览器缓存")
        print("  3. 测试前端进度显示")
    else:
        print("\n⚠️ 仍有问题需要进一步调试")
    
    print("\n🔚 测试完成")

if __name__ == "__main__":
    main()